"use client";

import React, { useState, useRef } from 'react';
import <PERSON> from 'papapar<PERSON>';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, FileText, Download, AlertCircle, CheckCircle2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface Tweet {
  Username: string;
  TwitterHandle: string;
  Date: string;
  Comment: string;
}

const TwitterDownloader = () => {
  const [status, setStatus] = useState('');
  const [error, setError] = useState('');
  const [fileName, setFileName] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processTwitterData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0];
      if (!file) return;

      setFileName(file.name);
      setIsProcessing(true);
      setStatus('Processing your Twitter data...');
      setError('');
      setProgress(0);

      const text = await file.text();
      const lines = text.split('\n').map(line => line.trim()).filter(Boolean);
      const twitterData: Tweet[] = [];
      
      for (let i = 0; i < lines.length; i++) {
        // Update progress
        setProgress(Math.round((i / lines.length) * 100));
        
        // Rest of your processing logic...
        if (lines[i].includes('Show more') || lines[i].includes('Show replies') || 
            lines[i].includes('From') || lines[i].includes('Ad') || 
            lines[i].includes('Click the link') || lines[i].includes('Privacy Statement')) {
          continue;
        }

        if (i + 4 < lines.length && 
            !lines[i].startsWith('@') && 
            lines[i + 1].startsWith('@') && 
            lines[i + 2] === '·' && 
            lines[i + 3] && 
            lines[i + 4]) {
          
          const tweet: Tweet = {
            Username: lines[i].replace('// this is the Username', '').trim(),
            TwitterHandle: lines[i + 1].replace('// this is the Twitter Handle', '').trim(),
            Date: lines[i + 3].replace('// this is the Date', '').trim(),
            Comment: lines[i + 4].replace('// this is the Comment', '').trim()
          };
          
          if (tweet.Username && tweet.TwitterHandle && tweet.Date && tweet.Comment) {
            twitterData.push(tweet);
          }
          
          i += 4;
        }
      }

      setProgress(100);

      const fields = ['Username', 'TwitterHandle', 'Date', 'Comment'];
      const csv = Papa.unparse({
        data: twitterData,
        fields
      });

      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'twitter_comments.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      setStatus(`Successfully processed ${twitterData.length} tweets`);
    } catch (error: any) {
      console.error('Error processing file:', error);
      setError(error.message || 'An error occurred while processing the file');
      setStatus('');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto overflow-hidden backdrop-blur-sm bg-white/90 shadow-2xl border border-white/20 rounded-2xl">
      <CardHeader className="space-y-2 pb-6 border-b bg-gradient-to-r from-indigo-500/5 to-purple-500/5">
        <CardTitle className="text-2xl font-bold flex items-center gap-3">
          <div className="p-3 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
            <FileText className="w-6 h-6" />
          </div>
          <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Twitter Data Processor
          </span>
        </CardTitle>
        <CardDescription className="text-base text-gray-600">
          Convert your Twitter data export into a structured CSV format
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-8 pt-8">
        <input
          type="file"
          accept=".txt"
          onChange={processTwitterData}
          ref={fileInputRef}
          className="hidden"
        />
        
        <div 
          onClick={() => fileInputRef.current?.click()}
          className="relative group cursor-pointer"
        >
          <div className="flex flex-col items-center justify-center p-10 border-2 border-dashed rounded-xl 
                        bg-gradient-to-r from-indigo-50/50 to-purple-50/50
                        group-hover:from-indigo-100/50 group-hover:to-purple-100/50
                        transition-all duration-300 ease-in-out
                        group-hover:border-indigo-400/50 border-gray-200">
            <div className="flex flex-col items-center space-y-4 text-center">
              {fileName ? (
                <>
                  <div className="h-20 w-20 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center
                                transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 ease-out">
                    <FileText className="h-10 w-10 text-white" />
                  </div>
                  <div>
                    <p className="text-lg font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                      {fileName}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">Click to choose a different file</p>
                  </div>
                </>
              ) : (
                <>
                  <div className="h-20 w-20 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center
                                transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 ease-out">
                    <Upload className="h-10 w-10 text-white" />
                  </div>
                  <div>
                    <p className="text-lg font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                      Choose a file
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Drag and drop your Twitter data file or click to browse
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {isProcessing && (
          <div className="space-y-3">
            <Progress value={progress} className="h-2 bg-gradient-to-r from-indigo-100 to-purple-100" />
            <p className="text-sm text-center text-gray-600 animate-pulse">
              {status} ({progress}%)
            </p>
          </div>
        )}

        {status && !isProcessing && (
          <Alert className="border-green-500/30 bg-green-50/50 text-green-800">
            <CheckCircle2 className="h-5 w-5 text-green-500" />
            <AlertDescription className="flex items-center gap-2 ml-2 text-base font-medium">
              {status}
            </AlertDescription>
          </Alert>
        )}
        
        {error && (
          <Alert variant="destructive" className="bg-red-50/50 border-red-500/30">
            <AlertCircle className="h-5 w-5" />
            <AlertDescription className="flex items-center gap-2 ml-2 text-base font-medium">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <div className="mt-8 p-6 rounded-xl bg-gradient-to-r from-gray-50/50 to-white/50 border border-gray-100">
          <h4 className="font-semibold text-gray-900 mb-3">Supported format:</h4>
          <ul className="space-y-2 text-gray-600">
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500" />
              Text file (.txt) exported from Twitter/X
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500" />
              Contains tweets with username, handle, date, and content
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default TwitterDownloader; 