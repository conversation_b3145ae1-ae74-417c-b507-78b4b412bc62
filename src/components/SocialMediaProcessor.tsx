"use client";

import React, { useState, useRef, useEffect } from 'react';
import <PERSON> from 'papa<PERSON><PERSON>';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, FileText, Download, AlertCircle, CheckCircle2, Star, Calendar } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";

type Platform = 'twitter' | 'youtube' | 'instagram' | 'facebook' | 'linkedin';

interface ProcessedData {
  [key: string]: string;
}

const PLATFORM_CONFIGS = {
  twitter: {
    name: 'Twitter',
    fields: ['Username', 'TwitterHandle', 'Date', 'Comment'],
    pattern: {
      lineCount: 5,
      conditions: (lines: string[], i: number) => (
        !lines[i].startsWith('@') && 
        lines[i + 1].startsWith('@') && 
        lines[i + 2] === '·' && 
        lines[i + 3] && 
        lines[i + 4]
      ),
      extract: (lines: string[], i: number) => ({
        Username: lines[i].trim(),
        TwitterHandle: lines[i + 1].trim(),
        Date: lines[i + 3].trim(),
        Comment: lines[i + 4].trim()
      })
    }
  },
  youtube: {
    name: 'YouTube',
    fields: ['Username', 'DaysAgo', 'Comment', 'Likes'],
    pattern: {
      lineCount: 7,
      conditions: (lines: string[], i: number) => (
        lines[i].startsWith('@') && 
        lines[i + 1].includes('ago') && 
        lines[i + 2] && 
        lines[i + 3]?.trim() && 
        lines[i + 4]?.trim() === 'Reply'
      ),
      extract: (lines: string[], i: number) => ({
        Username: lines[i].trim(),
        DaysAgo: lines[i + 1].trim(),
        Comment: lines[i + 2].trim(),
        Likes: lines[i + 3].trim() || '0'
      })
    }
  },
  instagram: {
    name: 'Instagram',
    fields: ['Username', 'Comment', 'ReplyDate'],
    pattern: {
      lineCount: 3,
      conditions: (lines: string[], i: number) => {
        const currentLine = lines[i] || '';
        const nextLine = lines[i + 1] || '';
        return (
          (currentLine.includes("'s profile picture") || 
           (!currentLine.includes('Reply') && nextLine && nextLine.includes('Reply')))
        );
      },
      extract: (lines: string[], i: number) => {
        let username, comment, replyDate;
        
        if (lines[i].includes("'s profile picture")) {
          username = lines[i].split("'s profile picture")[0];
          comment = lines[i + 1];
          replyDate = (lines[i + 2] || '').split('Reply')[0];
          i += 2;
        } else {
          comment = lines[i];
          replyDate = (lines[i + 1] || '').split('Reply')[0];
          username = comment.split('\n')[0];
          i += 1;
        }

        return {
          Username: username.trim(),
          Comment: comment.trim(),
          ReplyDate: replyDate.trim()
        };
      }
    }
  },
  facebook: {
    name: 'Facebook',
    fields: ['Username', 'Comment', 'TimeAgo'],
    pattern: {
      lineCount: 4,
      conditions: (lines: string[], i: number) => {
        if (!lines[i]) return false;

        const nextLines = lines.slice(i, i + 20);
        const timePattern = /^(\d+[hwdm])$/;
        
        const replyIndex = nextLines.findIndex(line => line === 'Reply');
        if (replyIndex === -1) return false;

        const hasTimeMarker = nextLines
          .slice(0, replyIndex)
          .some(line => timePattern.test(line.trim()));

        const currentLine = lines[i].trim();
        const prevLine = i > 0 ? lines[i - 1]?.trim() : '';
        
        return (
          currentLine &&
          !['Reply', 'Edited'].includes(currentLine) &&
          !timePattern.test(currentLine) &&
          hasTimeMarker &&
          (i === 0 || prevLine === 'Reply' || prevLine === 'Edited' || prevLine === '·')
        );
      },
      extract: (lines: string[], i: number) => {
        const nextLines = lines.slice(i, i + 20);
        const timePattern = /^(\d+[hwdm])$/;
        
        const replyIndex = nextLines.findIndex(line => line === 'Reply');
        
        const contentLines = nextLines.slice(0, replyIndex);
        
        const timeMarkerIndex = contentLines.findIndex(line => 
          timePattern.test(line.trim())
        );
        
        const username = lines[i].split('·')[0].trim();
        
        const commentLines = contentLines
          .filter((line, index) => 
            line && 
            index > 0 &&
            index !== timeMarkerIndex &&
            !['Reply', 'Edited', '·'].includes(line) &&
            !line.startsWith('May be a doodle')
          );
        
        const comment = commentLines.join(' ').trim();
        const timeAgo = contentLines[timeMarkerIndex];

        return {
          Username: username,
          Comment: comment,
          TimeAgo: timeAgo
        };
      }
    }
  },
  linkedin: {
    name: 'LinkedIn',
    fields: ['Username', 'Followers', 'TimeAgo', 'Comment'],
    pattern: {
      lineCount: 4,
      conditions: (lines: string[], i: number) => {
        if (!lines[i]) return false;
        return (
          lines[i + 1]?.includes('followers') ||
          (lines[i].startsWith("View") && lines[i].endsWith("profile")) ||
          (lines[i + 1]?.trim() === '' && 
           (lines[i + 2]?.includes('• 3rd+') || lines[i + 2]?.trim() === '--'))
        );
      },
      extract: (lines: string[], i: number, existingData: ProcessedData[]) => {
        let username = '';
        let followers = '0';
        let timeAgo = '';
        let comment = '';

        // Handle company profiles
        if (lines[i + 1]?.includes('followers')) {
          username = lines[i].trim();
          followers = lines[i + 1].split('followers')[0].trim();
          timeAgo = lines[i + 2]?.trim() || '';
          comment = lines[i + 3]?.trim() || '';
        } 
        // Handle all other profiles
        else {
          // For "View X's profile", use next line as username
          // For plain username, use current line
          username = lines[i].startsWith("View") ? lines[i + 1].trim() : lines[i].trim();
          
          // Look ahead for time and comment
          const nextLines = lines.slice(i + 2);
          
          // Find time indicator (1w, 6d, etc.)
          const timeIndex = nextLines.findIndex(line => 
            /^\d+[dwmy]$/.test(line.trim())
          );

          if (timeIndex !== -1) {
            timeAgo = nextLines[timeIndex].trim();
            
            // Look for first valid comment after time
            for (let j = timeIndex + 1; j < nextLines.length; j++) {
              const line = nextLines[j]?.trim();
              if (line && 
                  !line.includes('Like') && 
                  !line.includes('Reply') &&
                  !line.includes('• 3rd+') &&
                  !/^\d+$/.test(line) &&
                  !line.includes('|') &&
                  !line.includes('at ') &&
                  !line.includes('--') &&
                  !line.includes('Environmental')) {
                comment = line;
                break;
              }
            }
          }
        }

        // Skip invalid records
        if (!comment || !timeAgo) {
          return null;
        }

        return {
          Username: username,
          Followers: followers,
          TimeAgo: timeAgo,
          Comment: comment
        };
      }
    }
  }
} as const;

// First, add a helper function at the top of the file to handle the conversion
const toMutableArray = <T,>(arr: readonly T[]): T[] => [...arr];

// Keep validateApiKey outside as it doesn't use component state
const validateApiKey = async (key: string): Promise<boolean> => {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${key}`
      }
    });
    return response.ok;
  } catch (error) {
    return false;
  }
};

const SocialMediaProcessor = () => {
  const [status, setStatus] = useState('');
  const [error, setError] = useState('');
  const [fileName, setFileName] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedPlatform, setSelectedPlatform] = useState<Platform>('twitter');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [apiKey, setApiKey] = useState<string>('');
  const [showApiKeyDialog, setShowApiKeyDialog] = useState(false);
  const [isApiKeySet, setIsApiKeySet] = useState(false);

  useEffect(() => {
    const savedApiKey = localStorage.getItem('openai_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
      setIsApiKeySet(true);
    } else {
      setShowApiKeyDialog(true);
    }
  }, []);

  // Move checkApiKey inside the component
  const checkApiKey = async () => {
    if (!apiKey) {
      setError('Please enter an API key');
      setShowApiKeyDialog(true);
      return false;
    }
    
    const isValid = await validateApiKey(apiKey);
    if (!isValid) {
      setError('Invalid API key. Please check and try again.');
      setShowApiKeyDialog(true);
      setIsApiKeySet(false);
      return false;
    }
    
    return true;
  };

  const processData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const isValidKey = await checkApiKey();
    if (!isValidKey) {
      return;
    }

    try {
      const file = event.target.files?.[0];
      if (!file) return;

      setFileName(file.name);
      setIsProcessing(true);
      setStatus(`Processing your ${PLATFORM_CONFIGS[selectedPlatform].name} data...`);
      setError('');
      setProgress(0);

      const text = await file.text();
      const lines = text.split('\n').map((line: string) => line.trim());
      const processedData: ProcessedData[] = [];
      
      for (let i = 0; i < lines.length; i++) {
        setProgress(Math.round((i / lines.length) * 100));
        
        if (lines[i].includes('Show more') || lines[i].includes('Show replies') || 
            lines[i].includes('From') || lines[i].includes('Ad') || 
            lines[i].includes('Click the link') || lines[i].includes('Privacy Statement')) {
          continue;
        }

        if (selectedPlatform === 'twitter') {
          if (i + 4 < lines.length && 
              !lines[i].startsWith('@') && 
              lines[i + 1].startsWith('@') && 
              lines[i + 2] === '·' && 
              lines[i + 3] && 
              lines[i + 4]) {
            
            const tweet = {
              Username: lines[i].trim(),
              TwitterHandle: lines[i + 1].trim(),
              Date: lines[i + 3].trim(),
              Comment: lines[i + 4].trim()
            };
            
            if (tweet.Username && tweet.TwitterHandle && tweet.Date && tweet.Comment) {
              processedData.push(tweet);
            }
            
            i += 4;
          }
        } else if (selectedPlatform === 'instagram') {
          if (lines[i].includes("'s profile picture") || 
              (lines[i] && !lines[i].includes('Reply') && lines[i + 1]?.includes('Reply'))) {
            
            let username, comment, replyDate;
            
            if (lines[i].includes("'s profile picture")) {
              username = lines[i].split("'s profile picture")[0];
              comment = lines[i + 1];
              replyDate = (lines[i + 2] || '').split('Reply')[0];
              i += 2;
            } else {
              comment = lines[i];
              replyDate = (lines[i + 1] || '').split('Reply')[0];
              username = comment.split('\n')[0];
              i += 1;
            }

            if (comment && replyDate) {
              processedData.push({
                Username: username?.trim() || 'Anonymous',
                Comment: comment.trim(),
                ReplyDate: replyDate.trim()
              });
            }
          }
        } else if (selectedPlatform === 'youtube') {
          if (lines[i].startsWith('@')) {
            const nextLines = lines.slice(i, i + 7);
            const timeAgoLine = nextLines.find((line: string) => line.includes('ago'));
            const likesLine = nextLines.find((line: string) => /^\d+$/.test(line.trim()));
            const commentLine = nextLines.find((line: string) => 
              line && !line.startsWith('@') && 
              !line.includes('ago') && 
              !line.includes('Reply') && 
              !/^\d+$/.test(line.trim())
            );

            if (timeAgoLine && commentLine) {
              processedData.push({
                Username: lines[i].trim(),
                DaysAgo: timeAgoLine.trim(),
                Comment: commentLine.trim(),
                Likes: likesLine ? likesLine.trim() : '0'
              });
            }
            i += nextLines.findIndex((line: string) => line === 'Reply') + 1;
          }
        } else {
          const config = PLATFORM_CONFIGS[selectedPlatform as Platform];
          if (config.pattern.conditions(lines, i)) {
            const data = config.pattern.extract(lines, i, processedData);
            if (data) {
              processedData.push(data);
              const nextLines = lines.slice(i + 1);
              const nextMarkerIndex = nextLines.findIndex((line: string) => 
                line === 'Reply' || line === 'Edited'
              );
              if (nextMarkerIndex !== -1) {
                i += nextMarkerIndex + 1;
              } else {
                i++;
              }
            }
          }
        }
      }

      setProgress(100);

      const csv = Papa.unparse({
        fields: toMutableArray(PLATFORM_CONFIGS[selectedPlatform].fields),
        data: processedData
      });

      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `${selectedPlatform}_comments.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      setStatus(`Successfully processed ${processedData.length} comments`);
    } catch (error: any) {
      console.error('Error processing file:', error);
      setError(error.message || 'An error occurred while processing the file');
      setStatus('');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePlatformSelect = async (platform: Platform) => {
    const isValidKey = await checkApiKey();
    if (!isValidKey) {
      return;
    }
    
    setSelectedPlatform(platform);
    fileInputRef.current?.click();
  };

  const handleApiKeySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (apiKey.trim()) {
      // Validate the API key before saving
      const isValid = await validateApiKey(apiKey);
      if (isValid) {
        setIsApiKeySet(true);
        setShowApiKeyDialog(false);
        localStorage.setItem('openai_api_key', apiKey);
      } else {
        setError('Invalid API key. Please check and try again.');
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Processing Parameters Card */}
      <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8">
        <div className="flex items-center gap-3 mb-8">
          <div className="w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center">
            <Star className="w-5 h-5 text-white" />
          </div>
          <h2 className="text-2xl font-semibold text-white">Processing Parameters</h2>
        </div>

        <div className="space-y-6">
          {/* Platform Selection */}
          <div>
            <label className="block text-sm font-medium text-white/90 mb-3">
              PLATFORM:
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {(Object.keys(PLATFORM_CONFIGS) as Platform[]).map((platform) => (
                <button
                  key={platform}
                  onClick={() => handlePlatformSelect(platform)}
                  className={`p-3 rounded-lg border text-sm font-medium transition-all duration-200 ${
                    selectedPlatform === platform
                      ? 'bg-pink-500 border-pink-500 text-white'
                      : 'bg-white/10 border-white/30 text-white/80 hover:bg-white/20'
                  }`}
                >
                  {PLATFORM_CONFIGS[platform].name}
                </button>
              ))}
            </div>
          </div>

          {/* File Upload Section */}
          <div>
            <label className="block text-sm font-medium text-white/90 mb-3">
              DATA FILE:
            </label>
            <div 
              onClick={() => fileInputRef.current?.click()}
              className="relative cursor-pointer group"
            >
              <div className="flex items-center gap-4 p-4 rounded-lg bg-white/10 border border-white/30 hover:bg-white/20 transition-all duration-200">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <Upload className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-white font-medium">
                    {fileName || 'Choose file to upload'}
                  </p>
                  <p className="text-white/60 text-sm">
                    Select .txt file exported from {selectedPlatform || 'selected platform'}
                  </p>
                </div>
              </div>
            </div>
            <input
              type="file"
              accept=".txt"
              onChange={processData}
              ref={fileInputRef}
              className="hidden"
            />
          </div>

          {/* API Key Section */}
          <div>
            <label className="block text-sm font-medium text-white/90 mb-3">
              API CONFIGURATION:
            </label>
            <div className="flex gap-3">
              <div className="flex-1 p-3 rounded-lg bg-white/10 border border-white/30">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${isApiKeySet ? 'bg-green-400' : 'bg-red-400'}`}></div>
                  <span className="text-white text-sm">
                    {isApiKeySet ? 'API Key Configured' : 'API Key Required'}
                  </span>
                </div>
              </div>
              <button
                onClick={() => setShowApiKeyDialog(true)}
                className="px-4 py-3 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-all duration-200 text-sm font-medium"
              >
                Configure
              </button>
            </div>
          </div>

          {/* Output Preview */}
          {selectedPlatform && (
            <div>
              <label className="block text-sm font-medium text-white/90 mb-3">
                OUTPUT FORMAT:
              </label>
              <div className="p-4 rounded-lg bg-white/10 border border-white/30">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="w-4 h-4 text-white/60" />
                  <span className="text-white text-sm font-medium">CSV Export</span>
                </div>
                <p className="text-white/70 text-sm">
                  Columns: {PLATFORM_CONFIGS[selectedPlatform].fields.join(', ')}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Processing Status */}
        {isProcessing && (
          <div className="mt-6 space-y-3">
            <Progress value={progress} className="h-2" />
            <p className="text-center text-white/80 text-sm">
              {status} ({progress}%)
            </p>
          </div>
        )}

        {/* Success/Error Messages */}
        {status && !isProcessing && (
          <div className="mt-6 p-4 rounded-lg bg-green-500/20 border border-green-500/30">
            <div className="flex items-center gap-2 text-green-100">
              <CheckCircle2 className="w-5 h-5" />
              <span className="font-medium">{status}</span>
            </div>
          </div>
        )}
        
        {error && (
          <div className="mt-6 p-4 rounded-lg bg-red-500/20 border border-red-500/30">
            <div className="flex items-center gap-2 text-red-100">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* Process Button */}
        <div className="mt-8">
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={!selectedPlatform || !isApiKeySet || isProcessing}
            className="w-full py-4 bg-gradient-to-r from-pink-500 to-pink-600 text-white font-semibold rounded-lg hover:from-pink-600 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
          >
            <div className="flex items-center justify-center gap-2">
              <Upload className="w-5 h-5" />
              {isProcessing ? 'Processing...' : '✓ Process Data'}
            </div>
          </button>
        </div>
      </div>

      {/* API Key Dialog */}
      <AlertDialog open={showApiKeyDialog} onOpenChange={setShowApiKeyDialog}>
        <AlertDialogContent className="bg-white/95 backdrop-blur-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Configure OpenAI API Key</AlertDialogTitle>
            <AlertDialogDescription>
              Please enter your OpenAI API key to use this application.
              Your key will be stored locally and never sent to our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <form onSubmit={handleApiKeySubmit}>
            <div className="grid gap-4 py-4">
              <Input
                type="password"
                placeholder="sk-..."
                value={apiKey}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setApiKey(e.target.value)}
                required
                className="bg-white/50"
              />
              {error && (
                <p className="text-sm text-red-500">{error}</p>
              )}
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
              <AlertDialogAction type="submit" className="bg-pink-500 hover:bg-pink-600">
                Save API Key
              </AlertDialogAction>
            </AlertDialogFooter>
          </form>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SocialMediaProcessor; 