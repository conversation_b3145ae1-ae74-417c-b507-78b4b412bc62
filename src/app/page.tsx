export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-800">
      <header className="w-full px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-white text-xl font-medium">Social Media Analytics</h1>
          <div className="w-12 h-6 bg-white/20 rounded-full flex items-center px-1">
            <div className="w-4 h-4 bg-white rounded-full"></div>
          </div>
        </div>
      </header>

      <div className="text-center px-6 py-16 space-y-8">
        <div className="space-y-6">
          <h1 className="text-5xl md:text-6xl font-bold text-white leading-tight">
            Social Media Data <span className="text-purple-300">Processor</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Extract and analyze user data from social media platforms 
            with advanced filtering, real-time processing, and comprehensive 
            data export capabilities.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button className="px-8 py-3 bg-gradient-to-r from-pink-500 to-pink-600 text-white font-semibold rounded-lg hover:from-pink-600 hover:to-pink-700 transition-all duration-200 shadow-lg">
            Process Data
          </button>
          <button className="px-8 py-3 bg-white/20 text-white font-semibold rounded-lg hover:bg-white/30 transition-all duration-200 backdrop-blur-sm">
            Learn More
          </button>
        </div>
      </div>

      <div className="px-6 pb-16 text-center">
        <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 max-w-4xl mx-auto">
          <h2 className="text-2xl font-semibold text-white mb-4">UI Redesign Complete</h2>
          <p className="text-white/80">The interface has been redesigned to match the Apple App Store Analytics style.</p>
        </div>
      </div>
    </main>
  );
}