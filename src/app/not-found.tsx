import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-800">
      <div className="text-center p-8 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20">
        <h2 className="text-3xl font-bold text-white mb-4">Page Not Found</h2>
        <p className="text-white/80 mb-6">The page you're looking for doesn't exist or has been moved.</p>
        <Link href="/" className="px-6 py-3 bg-gradient-to-r from-pink-500 to-pink-600 text-white font-semibold rounded-lg hover:from-pink-600 hover:to-pink-700 transition-all duration-200 shadow-lg">
          Return Home
        </Link>
      </div>
    </div>
  );
}