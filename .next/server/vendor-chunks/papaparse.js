/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/papaparse";
exports.ids = ["vendor-chunks/papaparse"];
exports.modules = {

/***/ "(ssr)/./node_modules/papaparse/papaparse.js":
/*!*********************************************!*\
  !*** ./node_modules/papaparse/papaparse.js ***!
  \*********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/* @license\nPapa Parse\nv5.4.1\nhttps://github.com/mholt/PapaParse\nLicense: MIT\n*/\n\n(function(root, factory)\n{\n\t/* globals define */\n\tif (true)\n\t{\n\t\t// AMD. Register as an anonymous module.\n\t\t!(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\t}\n\telse {}\n\t// in strict mode we cannot access arguments.callee, so we need a named reference to\n\t// stringify the factory method for the blob worker\n\t// eslint-disable-next-line func-name\n}(this, function moduleFactory()\n{\n\t'use strict';\n\n\tvar global = (function() {\n\t\t// alternative method, similar to `Function('return this')()`\n\t\t// but without using `eval` (which is disabled when\n\t\t// using Content Security Policy).\n\n\t\tif (typeof self !== 'undefined') { return self; }\n\t\tif (typeof window !== 'undefined') { return window; }\n\t\tif (typeof global !== 'undefined') { return global; }\n\n\t\t// When running tests none of the above have been defined\n\t\treturn {};\n\t})();\n\n\n\tfunction getWorkerBlob() {\n\t\tvar URL = global.URL || global.webkitURL || null;\n\t\tvar code = moduleFactory.toString();\n\t\treturn Papa.BLOB_URL || (Papa.BLOB_URL = URL.createObjectURL(new Blob([\"var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; \", '(', code, ')();'], {type: 'text/javascript'})));\n\t}\n\n\tvar IS_WORKER = !global.document && !!global.postMessage,\n\t\tIS_PAPA_WORKER = global.IS_PAPA_WORKER || false;\n\n\tvar workers = {}, workerIdCounter = 0;\n\n\tvar Papa = {};\n\n\tPapa.parse = CsvToJson;\n\tPapa.unparse = JsonToCsv;\n\n\tPapa.RECORD_SEP = String.fromCharCode(30);\n\tPapa.UNIT_SEP = String.fromCharCode(31);\n\tPapa.BYTE_ORDER_MARK = '\\ufeff';\n\tPapa.BAD_DELIMITERS = ['\\r', '\\n', '\"', Papa.BYTE_ORDER_MARK];\n\tPapa.WORKERS_SUPPORTED = !IS_WORKER && !!global.Worker;\n\tPapa.NODE_STREAM_INPUT = 1;\n\n\t// Configurable chunk sizes for local and remote files, respectively\n\tPapa.LocalChunkSize = 1024 * 1024 * 10;\t// 10 MB\n\tPapa.RemoteChunkSize = 1024 * 1024 * 5;\t// 5 MB\n\tPapa.DefaultDelimiter = ',';\t\t\t// Used if not specified and detection fails\n\n\t// Exposed for testing and development only\n\tPapa.Parser = Parser;\n\tPapa.ParserHandle = ParserHandle;\n\tPapa.NetworkStreamer = NetworkStreamer;\n\tPapa.FileStreamer = FileStreamer;\n\tPapa.StringStreamer = StringStreamer;\n\tPapa.ReadableStreamStreamer = ReadableStreamStreamer;\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tPapa.DuplexStreamStreamer = DuplexStreamStreamer;\n\t}\n\n\tif (global.jQuery)\n\t{\n\t\tvar $ = global.jQuery;\n\t\t$.fn.parse = function(options)\n\t\t{\n\t\t\tvar config = options.config || {};\n\t\t\tvar queue = [];\n\n\t\t\tthis.each(function(idx)\n\t\t\t{\n\t\t\t\tvar supported = $(this).prop('tagName').toUpperCase() === 'INPUT'\n\t\t\t\t\t\t\t\t&& $(this).attr('type').toLowerCase() === 'file'\n\t\t\t\t\t\t\t\t&& global.FileReader;\n\n\t\t\t\tif (!supported || !this.files || this.files.length === 0)\n\t\t\t\t\treturn true;\t// continue to next input element\n\n\t\t\t\tfor (var i = 0; i < this.files.length; i++)\n\t\t\t\t{\n\t\t\t\t\tqueue.push({\n\t\t\t\t\t\tfile: this.files[i],\n\t\t\t\t\t\tinputElem: this,\n\t\t\t\t\t\tinstanceConfig: $.extend({}, config)\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tparseNextFile();\t// begin parsing\n\t\t\treturn this;\t\t// maintains chainability\n\n\n\t\t\tfunction parseNextFile()\n\t\t\t{\n\t\t\t\tif (queue.length === 0)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(options.complete))\n\t\t\t\t\t\toptions.complete();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar f = queue[0];\n\n\t\t\t\tif (isFunction(options.before))\n\t\t\t\t{\n\t\t\t\t\tvar returned = options.before(f.file, f.inputElem);\n\n\t\t\t\t\tif (typeof returned === 'object')\n\t\t\t\t\t{\n\t\t\t\t\t\tif (returned.action === 'abort')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\terror('AbortError', f.file, f.inputElem, returned.reason);\n\t\t\t\t\t\t\treturn;\t// Aborts all queued files immediately\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (returned.action === 'skip')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (typeof returned.config === 'object')\n\t\t\t\t\t\t\tf.instanceConfig = $.extend(f.instanceConfig, returned.config);\n\t\t\t\t\t}\n\t\t\t\t\telse if (returned === 'skip')\n\t\t\t\t\t{\n\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Wrap up the user's complete callback, if any, so that ours also gets executed\n\t\t\t\tvar userCompleteFunc = f.instanceConfig.complete;\n\t\t\t\tf.instanceConfig.complete = function(results)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(userCompleteFunc))\n\t\t\t\t\t\tuserCompleteFunc(results, f.file, f.inputElem);\n\t\t\t\t\tfileComplete();\n\t\t\t\t};\n\n\t\t\t\tPapa.parse(f.file, f.instanceConfig);\n\t\t\t}\n\n\t\t\tfunction error(name, file, elem, reason)\n\t\t\t{\n\t\t\t\tif (isFunction(options.error))\n\t\t\t\t\toptions.error({name: name}, file, elem, reason);\n\t\t\t}\n\n\t\t\tfunction fileComplete()\n\t\t\t{\n\t\t\t\tqueue.splice(0, 1);\n\t\t\t\tparseNextFile();\n\t\t\t}\n\t\t};\n\t}\n\n\n\tif (IS_PAPA_WORKER)\n\t{\n\t\tglobal.onmessage = workerThreadReceivedMessage;\n\t}\n\n\n\n\n\tfunction CsvToJson(_input, _config)\n\t{\n\t\t_config = _config || {};\n\t\tvar dynamicTyping = _config.dynamicTyping || false;\n\t\tif (isFunction(dynamicTyping)) {\n\t\t\t_config.dynamicTypingFunction = dynamicTyping;\n\t\t\t// Will be filled on first row call\n\t\t\tdynamicTyping = {};\n\t\t}\n\t\t_config.dynamicTyping = dynamicTyping;\n\n\t\t_config.transform = isFunction(_config.transform) ? _config.transform : false;\n\n\t\tif (_config.worker && Papa.WORKERS_SUPPORTED)\n\t\t{\n\t\t\tvar w = newWorker();\n\n\t\t\tw.userStep = _config.step;\n\t\t\tw.userChunk = _config.chunk;\n\t\t\tw.userComplete = _config.complete;\n\t\t\tw.userError = _config.error;\n\n\t\t\t_config.step = isFunction(_config.step);\n\t\t\t_config.chunk = isFunction(_config.chunk);\n\t\t\t_config.complete = isFunction(_config.complete);\n\t\t\t_config.error = isFunction(_config.error);\n\t\t\tdelete _config.worker;\t// prevent infinite loop\n\n\t\t\tw.postMessage({\n\t\t\t\tinput: _input,\n\t\t\t\tconfig: _config,\n\t\t\t\tworkerId: w.id\n\t\t\t});\n\n\t\t\treturn;\n\t\t}\n\n\t\tvar streamer = null;\n\t\tif (_input === Papa.NODE_STREAM_INPUT && typeof PAPA_BROWSER_CONTEXT === 'undefined')\n\t\t{\n\t\t\t// create a node Duplex stream for use\n\t\t\t// with .pipe\n\t\t\tstreamer = new DuplexStreamStreamer(_config);\n\t\t\treturn streamer.getStream();\n\t\t}\n\t\telse if (typeof _input === 'string')\n\t\t{\n\t\t\t_input = stripBom(_input);\n\t\t\tif (_config.download)\n\t\t\t\tstreamer = new NetworkStreamer(_config);\n\t\t\telse\n\t\t\t\tstreamer = new StringStreamer(_config);\n\t\t}\n\t\telse if (_input.readable === true && isFunction(_input.read) && isFunction(_input.on))\n\t\t{\n\t\t\tstreamer = new ReadableStreamStreamer(_config);\n\t\t}\n\t\telse if ((global.File && _input instanceof File) || _input instanceof Object)\t// ...Safari. (see issue #106)\n\t\t\tstreamer = new FileStreamer(_config);\n\n\t\treturn streamer.stream(_input);\n\n\t\t// Strip character from UTF-8 BOM encoded files that cause issue parsing the file\n\t\tfunction stripBom(string) {\n\t\t\tif (string.charCodeAt(0) === 0xfeff) {\n\t\t\t\treturn string.slice(1);\n\t\t\t}\n\t\t\treturn string;\n\t\t}\n\t}\n\n\n\n\n\n\n\tfunction JsonToCsv(_input, _config)\n\t{\n\t\t// Default configuration\n\n\t\t/** whether to surround every datum with quotes */\n\t\tvar _quotes = false;\n\n\t\t/** whether to write headers */\n\t\tvar _writeHeader = true;\n\n\t\t/** delimiting character(s) */\n\t\tvar _delimiter = ',';\n\n\t\t/** newline character(s) */\n\t\tvar _newline = '\\r\\n';\n\n\t\t/** quote character */\n\t\tvar _quoteChar = '\"';\n\n\t\t/** escaped quote character, either \"\" or <config.escapeChar>\" */\n\t\tvar _escapedQuote = _quoteChar + _quoteChar;\n\n\t\t/** whether to skip empty lines */\n\t\tvar _skipEmptyLines = false;\n\n\t\t/** the columns (keys) we expect when we unparse objects */\n\t\tvar _columns = null;\n\n\t\t/** whether to prevent outputting cells that can be parsed as formulae by spreadsheet software (Excel and LibreOffice) */\n\t\tvar _escapeFormulae = false;\n\n\t\tunpackConfig();\n\n\t\tvar quoteCharRegex = new RegExp(escapeRegExp(_quoteChar), 'g');\n\n\t\tif (typeof _input === 'string')\n\t\t\t_input = JSON.parse(_input);\n\n\t\tif (Array.isArray(_input))\n\t\t{\n\t\t\tif (!_input.length || Array.isArray(_input[0]))\n\t\t\t\treturn serialize(null, _input, _skipEmptyLines);\n\t\t\telse if (typeof _input[0] === 'object')\n\t\t\t\treturn serialize(_columns || Object.keys(_input[0]), _input, _skipEmptyLines);\n\t\t}\n\t\telse if (typeof _input === 'object')\n\t\t{\n\t\t\tif (typeof _input.data === 'string')\n\t\t\t\t_input.data = JSON.parse(_input.data);\n\n\t\t\tif (Array.isArray(_input.data))\n\t\t\t{\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields = _input.meta && _input.meta.fields || _columns;\n\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields =  Array.isArray(_input.data[0])\n\t\t\t\t\t\t? _input.fields\n\t\t\t\t\t\t: typeof _input.data[0] === 'object'\n\t\t\t\t\t\t\t? Object.keys(_input.data[0])\n\t\t\t\t\t\t\t: [];\n\n\t\t\t\tif (!(Array.isArray(_input.data[0])) && typeof _input.data[0] !== 'object')\n\t\t\t\t\t_input.data = [_input.data];\t// handles input like [1,2,3] or ['asdf']\n\t\t\t}\n\n\t\t\treturn serialize(_input.fields || [], _input.data || [], _skipEmptyLines);\n\t\t}\n\n\t\t// Default (any valid paths should return before this)\n\t\tthrow new Error('Unable to serialize unrecognized input');\n\n\n\t\tfunction unpackConfig()\n\t\t{\n\t\t\tif (typeof _config !== 'object')\n\t\t\t\treturn;\n\n\t\t\tif (typeof _config.delimiter === 'string'\n                && !Papa.BAD_DELIMITERS.filter(function(value) { return _config.delimiter.indexOf(value) !== -1; }).length)\n\t\t\t{\n\t\t\t\t_delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tif (typeof _config.quotes === 'boolean'\n\t\t\t\t|| typeof _config.quotes === 'function'\n\t\t\t\t|| Array.isArray(_config.quotes))\n\t\t\t\t_quotes = _config.quotes;\n\n\t\t\tif (typeof _config.skipEmptyLines === 'boolean'\n\t\t\t\t|| typeof _config.skipEmptyLines === 'string')\n\t\t\t\t_skipEmptyLines = _config.skipEmptyLines;\n\n\t\t\tif (typeof _config.newline === 'string')\n\t\t\t\t_newline = _config.newline;\n\n\t\t\tif (typeof _config.quoteChar === 'string')\n\t\t\t\t_quoteChar = _config.quoteChar;\n\n\t\t\tif (typeof _config.header === 'boolean')\n\t\t\t\t_writeHeader = _config.header;\n\n\t\t\tif (Array.isArray(_config.columns)) {\n\n\t\t\t\tif (_config.columns.length === 0) throw new Error('Option columns is empty');\n\n\t\t\t\t_columns = _config.columns;\n\t\t\t}\n\n\t\t\tif (_config.escapeChar !== undefined) {\n\t\t\t\t_escapedQuote = _config.escapeChar + _quoteChar;\n\t\t\t}\n\n\t\t\tif (typeof _config.escapeFormulae === 'boolean' || _config.escapeFormulae instanceof RegExp) {\n\t\t\t\t_escapeFormulae = _config.escapeFormulae instanceof RegExp ? _config.escapeFormulae : /^[=+\\-@\\t\\r].*$/;\n\t\t\t}\n\t\t}\n\n\t\t/** The double for loop that iterates the data and writes out a CSV string including header row */\n\t\tfunction serialize(fields, data, skipEmptyLines)\n\t\t{\n\t\t\tvar csv = '';\n\n\t\t\tif (typeof fields === 'string')\n\t\t\t\tfields = JSON.parse(fields);\n\t\t\tif (typeof data === 'string')\n\t\t\t\tdata = JSON.parse(data);\n\n\t\t\tvar hasHeader = Array.isArray(fields) && fields.length > 0;\n\t\t\tvar dataKeyedByField = !(Array.isArray(data[0]));\n\n\t\t\t// If there a header row, write it first\n\t\t\tif (hasHeader && _writeHeader)\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < fields.length; i++)\n\t\t\t\t{\n\t\t\t\t\tif (i > 0)\n\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\tcsv += safe(fields[i], i);\n\t\t\t\t}\n\t\t\t\tif (data.length > 0)\n\t\t\t\t\tcsv += _newline;\n\t\t\t}\n\n\t\t\t// Then write out the data\n\t\t\tfor (var row = 0; row < data.length; row++)\n\t\t\t{\n\t\t\t\tvar maxCol = hasHeader ? fields.length : data[row].length;\n\n\t\t\t\tvar emptyLine = false;\n\t\t\t\tvar nullLine = hasHeader ? Object.keys(data[row]).length === 0 : data[row].length === 0;\n\t\t\t\tif (skipEmptyLines && !hasHeader)\n\t\t\t\t{\n\t\t\t\t\temptyLine = skipEmptyLines === 'greedy' ? data[row].join('').trim() === '' : data[row].length === 1 && data[row][0].length === 0;\n\t\t\t\t}\n\t\t\t\tif (skipEmptyLines === 'greedy' && hasHeader) {\n\t\t\t\t\tvar line = [];\n\t\t\t\t\tfor (var c = 0; c < maxCol; c++) {\n\t\t\t\t\t\tvar cx = dataKeyedByField ? fields[c] : c;\n\t\t\t\t\t\tline.push(data[row][cx]);\n\t\t\t\t\t}\n\t\t\t\t\temptyLine = line.join('').trim() === '';\n\t\t\t\t}\n\t\t\t\tif (!emptyLine)\n\t\t\t\t{\n\t\t\t\t\tfor (var col = 0; col < maxCol; col++)\n\t\t\t\t\t{\n\t\t\t\t\t\tif (col > 0 && !nullLine)\n\t\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\t\tvar colIdx = hasHeader && dataKeyedByField ? fields[col] : col;\n\t\t\t\t\t\tcsv += safe(data[row][colIdx], col);\n\t\t\t\t\t}\n\t\t\t\t\tif (row < data.length - 1 && (!skipEmptyLines || (maxCol > 0 && !nullLine)))\n\t\t\t\t\t{\n\t\t\t\t\t\tcsv += _newline;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn csv;\n\t\t}\n\n\t\t/** Encloses a value around quotes if needed (makes a value safe for CSV insertion) */\n\t\tfunction safe(str, col)\n\t\t{\n\t\t\tif (typeof str === 'undefined' || str === null)\n\t\t\t\treturn '';\n\n\t\t\tif (str.constructor === Date)\n\t\t\t\treturn JSON.stringify(str).slice(1, 25);\n\n\t\t\tvar needsQuotes = false;\n\n\t\t\tif (_escapeFormulae && typeof str === \"string\" && _escapeFormulae.test(str)) {\n\t\t\t\tstr = \"'\" + str;\n\t\t\t\tneedsQuotes = true;\n\t\t\t}\n\n\t\t\tvar escapedQuoteStr = str.toString().replace(quoteCharRegex, _escapedQuote);\n\n\t\t\tneedsQuotes = needsQuotes\n\t\t\t\t\t\t\t|| _quotes === true\n\t\t\t\t\t\t\t|| (typeof _quotes === 'function' && _quotes(str, col))\n\t\t\t\t\t\t\t|| (Array.isArray(_quotes) && _quotes[col])\n\t\t\t\t\t\t\t|| hasAny(escapedQuoteStr, Papa.BAD_DELIMITERS)\n\t\t\t\t\t\t\t|| escapedQuoteStr.indexOf(_delimiter) > -1\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(0) === ' '\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(escapedQuoteStr.length - 1) === ' ';\n\n\t\t\treturn needsQuotes ? _quoteChar + escapedQuoteStr + _quoteChar : escapedQuoteStr;\n\t\t}\n\n\t\tfunction hasAny(str, substrings)\n\t\t{\n\t\t\tfor (var i = 0; i < substrings.length; i++)\n\t\t\t\tif (str.indexOf(substrings[i]) > -1)\n\t\t\t\t\treturn true;\n\t\t\treturn false;\n\t\t}\n\t}\n\n\t/** ChunkStreamer is the base prototype for various streamer implementations. */\n\tfunction ChunkStreamer(config)\n\t{\n\t\tthis._handle = null;\n\t\tthis._finished = false;\n\t\tthis._completed = false;\n\t\tthis._halted = false;\n\t\tthis._input = null;\n\t\tthis._baseIndex = 0;\n\t\tthis._partialLine = '';\n\t\tthis._rowCount = 0;\n\t\tthis._start = 0;\n\t\tthis._nextChunk = null;\n\t\tthis.isFirstChunk = true;\n\t\tthis._completeResults = {\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\t\treplaceConfig.call(this, config);\n\n\t\tthis.parseChunk = function(chunk, isFakeChunk)\n\t\t{\n\t\t\t// First chunk pre-processing\n\t\t\tif (this.isFirstChunk && isFunction(this._config.beforeFirstChunk))\n\t\t\t{\n\t\t\t\tvar modifiedChunk = this._config.beforeFirstChunk(chunk);\n\t\t\t\tif (modifiedChunk !== undefined)\n\t\t\t\t\tchunk = modifiedChunk;\n\t\t\t}\n\t\t\tthis.isFirstChunk = false;\n\t\t\tthis._halted = false;\n\n\t\t\t// Rejoin the line we likely just split in two by chunking the file\n\t\t\tvar aggregate = this._partialLine + chunk;\n\t\t\tthis._partialLine = '';\n\n\t\t\tvar results = this._handle.parse(aggregate, this._baseIndex, !this._finished);\n\n\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\tthis._halted = true;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar lastIndex = results.meta.cursor;\n\n\t\t\tif (!this._finished)\n\t\t\t{\n\t\t\t\tthis._partialLine = aggregate.substring(lastIndex - this._baseIndex);\n\t\t\t\tthis._baseIndex = lastIndex;\n\t\t\t}\n\n\t\t\tif (results && results.data)\n\t\t\t\tthis._rowCount += results.data.length;\n\n\t\t\tvar finishedIncludingPreview = this._finished || (this._config.preview && this._rowCount >= this._config.preview);\n\n\t\t\tif (IS_PAPA_WORKER)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tresults: results,\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tfinished: finishedIncludingPreview\n\t\t\t\t});\n\t\t\t}\n\t\t\telse if (isFunction(this._config.chunk) && !isFakeChunk)\n\t\t\t{\n\t\t\t\tthis._config.chunk(results, this._handle);\n\t\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\t\tthis._halted = true;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tresults = undefined;\n\t\t\t\tthis._completeResults = undefined;\n\t\t\t}\n\n\t\t\tif (!this._config.step && !this._config.chunk) {\n\t\t\t\tthis._completeResults.data = this._completeResults.data.concat(results.data);\n\t\t\t\tthis._completeResults.errors = this._completeResults.errors.concat(results.errors);\n\t\t\t\tthis._completeResults.meta = results.meta;\n\t\t\t}\n\n\t\t\tif (!this._completed && finishedIncludingPreview && isFunction(this._config.complete) && (!results || !results.meta.aborted)) {\n\t\t\t\tthis._config.complete(this._completeResults, this._input);\n\t\t\t\tthis._completed = true;\n\t\t\t}\n\n\t\t\tif (!finishedIncludingPreview && (!results || !results.meta.paused))\n\t\t\t\tthis._nextChunk();\n\n\t\t\treturn results;\n\t\t};\n\n\t\tthis._sendError = function(error)\n\t\t{\n\t\t\tif (isFunction(this._config.error))\n\t\t\t\tthis._config.error(error);\n\t\t\telse if (IS_PAPA_WORKER && this._config.error)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\terror: error,\n\t\t\t\t\tfinished: false\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\n\t\tfunction replaceConfig(config)\n\t\t{\n\t\t\t// Deep-copy the config so we can edit it\n\t\t\tvar configCopy = copy(config);\n\t\t\tconfigCopy.chunkSize = parseInt(configCopy.chunkSize);\t// parseInt VERY important so we don't concatenate strings!\n\t\t\tif (!config.step && !config.chunk)\n\t\t\t\tconfigCopy.chunkSize = null;  // disable Range header if not streaming; bad values break IIS - see issue #196\n\t\t\tthis._handle = new ParserHandle(configCopy);\n\t\t\tthis._handle.streamer = this;\n\t\t\tthis._config = configCopy;\t// persist the copy to the caller\n\t\t}\n\t}\n\n\n\tfunction NetworkStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.RemoteChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar xhr;\n\n\t\tif (IS_WORKER)\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t\tthis._chunkLoaded();\n\t\t\t};\n\t\t}\n\t\telse\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t};\n\t\t}\n\n\t\tthis.stream = function(url)\n\t\t{\n\t\t\tthis._input = url;\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tif (this._finished)\n\t\t\t{\n\t\t\t\tthis._chunkLoaded();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\txhr = new XMLHttpRequest();\n\n\t\t\tif (this._config.withCredentials)\n\t\t\t{\n\t\t\t\txhr.withCredentials = this._config.withCredentials;\n\t\t\t}\n\n\t\t\tif (!IS_WORKER)\n\t\t\t{\n\t\t\t\txhr.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\txhr.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\n\t\t\txhr.open(this._config.downloadRequestBody ? 'POST' : 'GET', this._input, !IS_WORKER);\n\t\t\t// Headers can only be set when once the request state is OPENED\n\t\t\tif (this._config.downloadRequestHeaders)\n\t\t\t{\n\t\t\t\tvar headers = this._config.downloadRequestHeaders;\n\n\t\t\t\tfor (var headerName in headers)\n\t\t\t\t{\n\t\t\t\t\txhr.setRequestHeader(headerName, headers[headerName]);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = this._start + this._config.chunkSize - 1;\t// minus one because byte range is inclusive\n\t\t\t\txhr.setRequestHeader('Range', 'bytes=' + this._start + '-' + end);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\txhr.send(this._config.downloadRequestBody);\n\t\t\t}\n\t\t\tcatch (err) {\n\t\t\t\tthis._chunkError(err.message);\n\t\t\t}\n\n\t\t\tif (IS_WORKER && xhr.status === 0)\n\t\t\t\tthis._chunkError();\n\t\t};\n\n\t\tthis._chunkLoaded = function()\n\t\t{\n\t\t\tif (xhr.readyState !== 4)\n\t\t\t\treturn;\n\n\t\t\tif (xhr.status < 200 || xhr.status >= 400)\n\t\t\t{\n\t\t\t\tthis._chunkError();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Use chunckSize as it may be a diference on reponse lentgh due to characters with more than 1 byte\n\t\t\tthis._start += this._config.chunkSize ? this._config.chunkSize : xhr.responseText.length;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= getFileSize(xhr);\n\t\t\tthis.parseChunk(xhr.responseText);\n\t\t};\n\n\t\tthis._chunkError = function(errorMessage)\n\t\t{\n\t\t\tvar errorText = xhr.statusText || errorMessage;\n\t\t\tthis._sendError(new Error(errorText));\n\t\t};\n\n\t\tfunction getFileSize(xhr)\n\t\t{\n\t\t\tvar contentRange = xhr.getResponseHeader('Content-Range');\n\t\t\tif (contentRange === null) { // no content range, then finish!\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\treturn parseInt(contentRange.substring(contentRange.lastIndexOf('/') + 1));\n\t\t}\n\t}\n\tNetworkStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tNetworkStreamer.prototype.constructor = NetworkStreamer;\n\n\n\tfunction FileStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.LocalChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar reader, slice;\n\n\t\t// FileReader is better than FileReaderSync (even in worker) - see http://stackoverflow.com/q/24708649/1048862\n\t\t// But Firefox is a pill, too - see issue #76: https://github.com/mholt/PapaParse/issues/76\n\t\tvar usingAsyncReader = typeof FileReader !== 'undefined';\t// Safari doesn't consider it a function - see issue #105\n\n\t\tthis.stream = function(file)\n\t\t{\n\t\t\tthis._input = file;\n\t\t\tslice = file.slice || file.webkitSlice || file.mozSlice;\n\n\t\t\tif (usingAsyncReader)\n\t\t\t{\n\t\t\t\treader = new FileReader();\t\t// Preferred method of reading files, even in workers\n\t\t\t\treader.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\treader.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\t\t\telse\n\t\t\t\treader = new FileReaderSync();\t// Hack for running in a web worker in Firefox\n\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (!this._finished && (!this._config.preview || this._rowCount < this._config.preview))\n\t\t\t\tthis._readChunk();\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tvar input = this._input;\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = Math.min(this._start + this._config.chunkSize, this._input.size);\n\t\t\t\tinput = slice.call(input, this._start, end);\n\t\t\t}\n\t\t\tvar txt = reader.readAsText(input, this._config.encoding);\n\t\t\tif (!usingAsyncReader)\n\t\t\t\tthis._chunkLoaded({ target: { result: txt } });\t// mimic the async signature\n\t\t};\n\n\t\tthis._chunkLoaded = function(event)\n\t\t{\n\t\t\t// Very important to increment start each time before handling results\n\t\t\tthis._start += this._config.chunkSize;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= this._input.size;\n\t\t\tthis.parseChunk(event.target.result);\n\t\t};\n\n\t\tthis._chunkError = function()\n\t\t{\n\t\t\tthis._sendError(reader.error);\n\t\t};\n\n\t}\n\tFileStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tFileStreamer.prototype.constructor = FileStreamer;\n\n\n\tfunction StringStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar remaining;\n\t\tthis.stream = function(s)\n\t\t{\n\t\t\tremaining = s;\n\t\t\treturn this._nextChunk();\n\t\t};\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (this._finished) return;\n\t\t\tvar size = this._config.chunkSize;\n\t\t\tvar chunk;\n\t\t\tif(size) {\n\t\t\t\tchunk = remaining.substring(0, size);\n\t\t\t\tremaining = remaining.substring(size);\n\t\t\t} else {\n\t\t\t\tchunk = remaining;\n\t\t\t\tremaining = '';\n\t\t\t}\n\t\t\tthis._finished = !remaining;\n\t\t\treturn this.parseChunk(chunk);\n\t\t};\n\t}\n\tStringStreamer.prototype = Object.create(StringStreamer.prototype);\n\tStringStreamer.prototype.constructor = StringStreamer;\n\n\n\tfunction ReadableStreamStreamer(config)\n\t{\n\t\tconfig = config || {};\n\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar queue = [];\n\t\tvar parseOnData = true;\n\t\tvar streamHasEnded = false;\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.pause.apply(this, arguments);\n\t\t\tthis._input.pause();\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.resume.apply(this, arguments);\n\t\t\tthis._input.resume();\n\t\t};\n\n\t\tthis.stream = function(stream)\n\t\t{\n\t\t\tthis._input = stream;\n\n\t\t\tthis._input.on('data', this._streamData);\n\t\t\tthis._input.on('end', this._streamEnd);\n\t\t\tthis._input.on('error', this._streamError);\n\t\t};\n\n\t\tthis._checkIsFinished = function()\n\t\t{\n\t\t\tif (streamHasEnded && queue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tthis._checkIsFinished();\n\t\t\tif (queue.length)\n\t\t\t{\n\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tparseOnData = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._streamData = bindFunction(function(chunk)\n\t\t{\n\t\t\ttry\n\t\t\t{\n\t\t\t\tqueue.push(typeof chunk === 'string' ? chunk : chunk.toString(this._config.encoding));\n\n\t\t\t\tif (parseOnData)\n\t\t\t\t{\n\t\t\t\t\tparseOnData = false;\n\t\t\t\t\tthis._checkIsFinished();\n\t\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t\t}\n\t\t\t}\n\t\t\tcatch (error)\n\t\t\t{\n\t\t\t\tthis._streamError(error);\n\t\t\t}\n\t\t}, this);\n\n\t\tthis._streamError = bindFunction(function(error)\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tthis._sendError(error);\n\t\t}, this);\n\n\t\tthis._streamEnd = bindFunction(function()\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tstreamHasEnded = true;\n\t\t\tthis._streamData('');\n\t\t}, this);\n\n\t\tthis._streamCleanUp = bindFunction(function()\n\t\t{\n\t\t\tthis._input.removeListener('data', this._streamData);\n\t\t\tthis._input.removeListener('end', this._streamEnd);\n\t\t\tthis._input.removeListener('error', this._streamError);\n\t\t}, this);\n\t}\n\tReadableStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tReadableStreamStreamer.prototype.constructor = ReadableStreamStreamer;\n\n\n\tfunction DuplexStreamStreamer(_config) {\n\t\tvar Duplex = (__webpack_require__(/*! stream */ \"stream\").Duplex);\n\t\tvar config = copy(_config);\n\t\tvar parseOnWrite = true;\n\t\tvar writeStreamHasFinished = false;\n\t\tvar parseCallbackQueue = [];\n\t\tvar stream = null;\n\n\t\tthis._onCsvData = function(results)\n\t\t{\n\t\t\tvar data = results.data;\n\t\t\tif (!stream.push(data) && !this._handle.paused()) {\n\t\t\t\t// the writeable consumer buffer has filled up\n\t\t\t\t// so we need to pause until more items\n\t\t\t\t// can be processed\n\t\t\t\tthis._handle.pause();\n\t\t\t}\n\t\t};\n\n\t\tthis._onCsvComplete = function()\n\t\t{\n\t\t\t// node will finish the read stream when\n\t\t\t// null is pushed\n\t\t\tstream.push(null);\n\t\t};\n\n\t\tconfig.step = bindFunction(this._onCsvData, this);\n\t\tconfig.complete = bindFunction(this._onCsvComplete, this);\n\t\tChunkStreamer.call(this, config);\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (writeStreamHasFinished && parseCallbackQueue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t\tif (parseCallbackQueue.length) {\n\t\t\t\tparseCallbackQueue.shift()();\n\t\t\t} else {\n\t\t\t\tparseOnWrite = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._addToParseQueue = function(chunk, callback)\n\t\t{\n\t\t\t// add to queue so that we can indicate\n\t\t\t// completion via callback\n\t\t\t// node will automatically pause the incoming stream\n\t\t\t// when too many items have been added without their\n\t\t\t// callback being invoked\n\t\t\tparseCallbackQueue.push(bindFunction(function() {\n\t\t\t\tthis.parseChunk(typeof chunk === 'string' ? chunk : chunk.toString(config.encoding));\n\t\t\t\tif (isFunction(callback)) {\n\t\t\t\t\treturn callback();\n\t\t\t\t}\n\t\t\t}, this));\n\t\t\tif (parseOnWrite) {\n\t\t\t\tparseOnWrite = false;\n\t\t\t\tthis._nextChunk();\n\t\t\t}\n\t\t};\n\n\t\tthis._onRead = function()\n\t\t{\n\t\t\tif (this._handle.paused()) {\n\t\t\t\t// the writeable consumer can handle more data\n\t\t\t\t// so resume the chunk parsing\n\t\t\t\tthis._handle.resume();\n\t\t\t}\n\t\t};\n\n\t\tthis._onWrite = function(chunk, encoding, callback)\n\t\t{\n\t\t\tthis._addToParseQueue(chunk, callback);\n\t\t};\n\n\t\tthis._onWriteComplete = function()\n\t\t{\n\t\t\twriteStreamHasFinished = true;\n\t\t\t// have to write empty string\n\t\t\t// so parser knows its done\n\t\t\tthis._addToParseQueue('');\n\t\t};\n\n\t\tthis.getStream = function()\n\t\t{\n\t\t\treturn stream;\n\t\t};\n\t\tstream = new Duplex({\n\t\t\treadableObjectMode: true,\n\t\t\tdecodeStrings: false,\n\t\t\tread: bindFunction(this._onRead, this),\n\t\t\twrite: bindFunction(this._onWrite, this)\n\t\t});\n\t\tstream.once('finish', bindFunction(this._onWriteComplete, this));\n\t}\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tDuplexStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\t\tDuplexStreamStreamer.prototype.constructor = DuplexStreamStreamer;\n\t}\n\n\n\t// Use one ParserHandle per entire CSV file or string\n\tfunction ParserHandle(_config)\n\t{\n\t\t// One goal is to minimize the use of regular expressions...\n\t\tvar MAX_FLOAT = Math.pow(2, 53);\n\t\tvar MIN_FLOAT = -MAX_FLOAT;\n\t\tvar FLOAT = /^\\s*-?(\\d+\\.?|\\.\\d+|\\d+\\.\\d+)([eE][-+]?\\d+)?\\s*$/;\n\t\tvar ISO_DATE = /^((\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)))$/;\n\t\tvar self = this;\n\t\tvar _stepCounter = 0;\t// Number of times step was called (number of rows parsed)\n\t\tvar _rowCounter = 0;\t// Number of rows that have been parsed so far\n\t\tvar _input;\t\t\t\t// The input being parsed\n\t\tvar _parser;\t\t\t// The core parser being used\n\t\tvar _paused = false;\t// Whether we are paused or not\n\t\tvar _aborted = false;\t// Whether the parser has aborted or not\n\t\tvar _delimiterError;\t// Temporary state between delimiter detection and processing results\n\t\tvar _fields = [];\t\t// Fields are from the header row of the input, if there is one\n\t\tvar _results = {\t\t// The last results returned from the parser\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\n\t\tif (isFunction(_config.step))\n\t\t{\n\t\t\tvar userStep = _config.step;\n\t\t\t_config.step = function(results)\n\t\t\t{\n\t\t\t\t_results = results;\n\n\t\t\t\tif (needsHeaderRow())\n\t\t\t\t\tprocessResults();\n\t\t\t\telse\t// only call user's step function after header row\n\t\t\t\t{\n\t\t\t\t\tprocessResults();\n\n\t\t\t\t\t// It's possbile that this line was empty and there's no row here after all\n\t\t\t\t\tif (_results.data.length === 0)\n\t\t\t\t\t\treturn;\n\n\t\t\t\t\t_stepCounter += results.data.length;\n\t\t\t\t\tif (_config.preview && _stepCounter > _config.preview)\n\t\t\t\t\t\t_parser.abort();\n\t\t\t\t\telse {\n\t\t\t\t\t\t_results.data = _results.data[0];\n\t\t\t\t\t\tuserStep(_results, self);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\n\t\t/**\n\t\t * Parses input. Most users won't need, and shouldn't mess with, the baseIndex\n\t\t * and ignoreLastRow parameters. They are used by streamers (wrapper functions)\n\t\t * when an input comes in multiple chunks, like from a file.\n\t\t */\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\tvar quoteChar = _config.quoteChar || '\"';\n\t\t\tif (!_config.newline)\n\t\t\t\t_config.newline = guessLineEndings(input, quoteChar);\n\n\t\t\t_delimiterError = false;\n\t\t\tif (!_config.delimiter)\n\t\t\t{\n\t\t\t\tvar delimGuess = guessDelimiter(input, _config.newline, _config.skipEmptyLines, _config.comments, _config.delimitersToGuess);\n\t\t\t\tif (delimGuess.successful)\n\t\t\t\t\t_config.delimiter = delimGuess.bestDelimiter;\n\t\t\t\telse\n\t\t\t\t{\n\t\t\t\t\t_delimiterError = true;\t// add error after parsing (otherwise it would be overwritten)\n\t\t\t\t\t_config.delimiter = Papa.DefaultDelimiter;\n\t\t\t\t}\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\t\t\telse if(isFunction(_config.delimiter))\n\t\t\t{\n\t\t\t\t_config.delimiter = _config.delimiter(input);\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tvar parserConfig = copy(_config);\n\t\t\tif (_config.preview && _config.header)\n\t\t\t\tparserConfig.preview++;\t// to compensate for header row\n\n\t\t\t_input = input;\n\t\t\t_parser = new Parser(parserConfig);\n\t\t\t_results = _parser.parse(_input, baseIndex, ignoreLastRow);\n\t\t\tprocessResults();\n\t\t\treturn _paused ? { meta: { paused: true } } : (_results || { meta: { paused: false } });\n\t\t};\n\n\t\tthis.paused = function()\n\t\t{\n\t\t\treturn _paused;\n\t\t};\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\t_paused = true;\n\t\t\t_parser.abort();\n\n\t\t\t// If it is streaming via \"chunking\", the reader will start appending correctly already so no need to substring,\n\t\t\t// otherwise we can get duplicate content within a row\n\t\t\t_input = isFunction(_config.chunk) ? \"\" : _input.substring(_parser.getCharIndex());\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tif(self.streamer._halted) {\n\t\t\t\t_paused = false;\n\t\t\t\tself.streamer.parseChunk(_input, true);\n\t\t\t} else {\n\t\t\t\t// Bugfix: #636 In case the processing hasn't halted yet\n\t\t\t\t// wait for it to halt in order to resume\n\t\t\t\tsetTimeout(self.resume, 3);\n\t\t\t}\n\t\t};\n\n\t\tthis.aborted = function()\n\t\t{\n\t\t\treturn _aborted;\n\t\t};\n\n\t\tthis.abort = function()\n\t\t{\n\t\t\t_aborted = true;\n\t\t\t_parser.abort();\n\t\t\t_results.meta.aborted = true;\n\t\t\tif (isFunction(_config.complete))\n\t\t\t\t_config.complete(_results);\n\t\t\t_input = '';\n\t\t};\n\n\t\tfunction testEmptyLine(s) {\n\t\t\treturn _config.skipEmptyLines === 'greedy' ? s.join('').trim() === '' : s.length === 1 && s[0].length === 0;\n\t\t}\n\n\t\tfunction testFloat(s) {\n\t\t\tif (FLOAT.test(s)) {\n\t\t\t\tvar floatValue = parseFloat(s);\n\t\t\t\tif (floatValue > MIN_FLOAT && floatValue < MAX_FLOAT) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction processResults()\n\t\t{\n\t\t\tif (_results && _delimiterError)\n\t\t\t{\n\t\t\t\taddError('Delimiter', 'UndetectableDelimiter', 'Unable to auto-detect delimiting character; defaulted to \\'' + Papa.DefaultDelimiter + '\\'');\n\t\t\t\t_delimiterError = false;\n\t\t\t}\n\n\t\t\tif (_config.skipEmptyLines)\n\t\t\t{\n\t\t\t\t_results.data = _results.data.filter(function(d) {\n\t\t\t\t\treturn !testEmptyLine(d);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (needsHeaderRow())\n\t\t\t\tfillHeaderFields();\n\n\t\t\treturn applyHeaderAndDynamicTypingAndTransformation();\n\t\t}\n\n\t\tfunction needsHeaderRow()\n\t\t{\n\t\t\treturn _config.header && _fields.length === 0;\n\t\t}\n\n\t\tfunction fillHeaderFields()\n\t\t{\n\t\t\tif (!_results)\n\t\t\t\treturn;\n\n\t\t\tfunction addHeader(header, i)\n\t\t\t{\n\t\t\t\tif (isFunction(_config.transformHeader))\n\t\t\t\t\theader = _config.transformHeader(header, i);\n\n\t\t\t\t_fields.push(header);\n\t\t\t}\n\n\t\t\tif (Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\tfor (var i = 0; needsHeaderRow() && i < _results.data.length; i++)\n\t\t\t\t\t_results.data[i].forEach(addHeader);\n\n\t\t\t\t_results.data.splice(0, 1);\n\t\t\t}\n\t\t\t// if _results.data[0] is not an array, we are in a step where _results.data is the row.\n\t\t\telse\n\t\t\t\t_results.data.forEach(addHeader);\n\t\t}\n\n\t\tfunction shouldApplyDynamicTyping(field) {\n\t\t\t// Cache function values to avoid calling it for each row\n\t\t\tif (_config.dynamicTypingFunction && _config.dynamicTyping[field] === undefined) {\n\t\t\t\t_config.dynamicTyping[field] = _config.dynamicTypingFunction(field);\n\t\t\t}\n\t\t\treturn (_config.dynamicTyping[field] || _config.dynamicTyping) === true;\n\t\t}\n\n\t\tfunction parseDynamic(field, value)\n\t\t{\n\t\t\tif (shouldApplyDynamicTyping(field))\n\t\t\t{\n\t\t\t\tif (value === 'true' || value === 'TRUE')\n\t\t\t\t\treturn true;\n\t\t\t\telse if (value === 'false' || value === 'FALSE')\n\t\t\t\t\treturn false;\n\t\t\t\telse if (testFloat(value))\n\t\t\t\t\treturn parseFloat(value);\n\t\t\t\telse if (ISO_DATE.test(value))\n\t\t\t\t\treturn new Date(value);\n\t\t\t\telse\n\t\t\t\t\treturn (value === '' ? null : value);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tfunction applyHeaderAndDynamicTypingAndTransformation()\n\t\t{\n\t\t\tif (!_results || (!_config.header && !_config.dynamicTyping && !_config.transform))\n\t\t\t\treturn _results;\n\n\t\t\tfunction processRow(rowSource, i)\n\t\t\t{\n\t\t\t\tvar row = _config.header ? {} : [];\n\n\t\t\t\tvar j;\n\t\t\t\tfor (j = 0; j < rowSource.length; j++)\n\t\t\t\t{\n\t\t\t\t\tvar field = j;\n\t\t\t\t\tvar value = rowSource[j];\n\n\t\t\t\t\tif (_config.header)\n\t\t\t\t\t\tfield = j >= _fields.length ? '__parsed_extra' : _fields[j];\n\n\t\t\t\t\tif (_config.transform)\n\t\t\t\t\t\tvalue = _config.transform(value,field);\n\n\t\t\t\t\tvalue = parseDynamic(field, value);\n\n\t\t\t\t\tif (field === '__parsed_extra')\n\t\t\t\t\t{\n\t\t\t\t\t\trow[field] = row[field] || [];\n\t\t\t\t\t\trow[field].push(value);\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\trow[field] = value;\n\t\t\t\t}\n\n\n\t\t\t\tif (_config.header)\n\t\t\t\t{\n\t\t\t\t\tif (j > _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooManyFields', 'Too many fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t\telse if (j < _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooFewFields', 'Too few fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t}\n\n\t\t\t\treturn row;\n\t\t\t}\n\n\t\t\tvar incrementBy = 1;\n\t\t\tif (!_results.data.length || Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\t_results.data = _results.data.map(processRow);\n\t\t\t\tincrementBy = _results.data.length;\n\t\t\t}\n\t\t\telse\n\t\t\t\t_results.data = processRow(_results.data, 0);\n\n\n\t\t\tif (_config.header && _results.meta)\n\t\t\t\t_results.meta.fields = _fields;\n\n\t\t\t_rowCounter += incrementBy;\n\t\t\treturn _results;\n\t\t}\n\n\t\tfunction guessDelimiter(input, newline, skipEmptyLines, comments, delimitersToGuess) {\n\t\t\tvar bestDelim, bestDelta, fieldCountPrevRow, maxFieldCount;\n\n\t\t\tdelimitersToGuess = delimitersToGuess || [',', '\\t', '|', ';', Papa.RECORD_SEP, Papa.UNIT_SEP];\n\n\t\t\tfor (var i = 0; i < delimitersToGuess.length; i++) {\n\t\t\t\tvar delim = delimitersToGuess[i];\n\t\t\t\tvar delta = 0, avgFieldCount = 0, emptyLinesCount = 0;\n\t\t\t\tfieldCountPrevRow = undefined;\n\n\t\t\t\tvar preview = new Parser({\n\t\t\t\t\tcomments: comments,\n\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\tnewline: newline,\n\t\t\t\t\tpreview: 10\n\t\t\t\t}).parse(input);\n\n\t\t\t\tfor (var j = 0; j < preview.data.length; j++) {\n\t\t\t\t\tif (skipEmptyLines && testEmptyLine(preview.data[j])) {\n\t\t\t\t\t\temptyLinesCount++;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tvar fieldCount = preview.data[j].length;\n\t\t\t\t\tavgFieldCount += fieldCount;\n\n\t\t\t\t\tif (typeof fieldCountPrevRow === 'undefined') {\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\telse if (fieldCount > 0) {\n\t\t\t\t\t\tdelta += Math.abs(fieldCount - fieldCountPrevRow);\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (preview.data.length > 0)\n\t\t\t\t\tavgFieldCount /= (preview.data.length - emptyLinesCount);\n\n\t\t\t\tif ((typeof bestDelta === 'undefined' || delta <= bestDelta)\n\t\t\t\t\t&& (typeof maxFieldCount === 'undefined' || avgFieldCount > maxFieldCount) && avgFieldCount > 1.99) {\n\t\t\t\t\tbestDelta = delta;\n\t\t\t\t\tbestDelim = delim;\n\t\t\t\t\tmaxFieldCount = avgFieldCount;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t_config.delimiter = bestDelim;\n\n\t\t\treturn {\n\t\t\t\tsuccessful: !!bestDelim,\n\t\t\t\tbestDelimiter: bestDelim\n\t\t\t};\n\t\t}\n\n\t\tfunction guessLineEndings(input, quoteChar)\n\t\t{\n\t\t\tinput = input.substring(0, 1024 * 1024);\t// max length 1 MB\n\t\t\t// Replace all the text inside quotes\n\t\t\tvar re = new RegExp(escapeRegExp(quoteChar) + '([^]*?)' + escapeRegExp(quoteChar), 'gm');\n\t\t\tinput = input.replace(re, '');\n\n\t\t\tvar r = input.split('\\r');\n\n\t\t\tvar n = input.split('\\n');\n\n\t\t\tvar nAppearsFirst = (n.length > 1 && n[0].length < r[0].length);\n\n\t\t\tif (r.length === 1 || nAppearsFirst)\n\t\t\t\treturn '\\n';\n\n\t\t\tvar numWithN = 0;\n\t\t\tfor (var i = 0; i < r.length; i++)\n\t\t\t{\n\t\t\t\tif (r[i][0] === '\\n')\n\t\t\t\t\tnumWithN++;\n\t\t\t}\n\n\t\t\treturn numWithN >= r.length / 2 ? '\\r\\n' : '\\r';\n\t\t}\n\n\t\tfunction addError(type, code, msg, row)\n\t\t{\n\t\t\tvar error = {\n\t\t\t\ttype: type,\n\t\t\t\tcode: code,\n\t\t\t\tmessage: msg\n\t\t\t};\n\t\t\tif(row !== undefined) {\n\t\t\t\terror.row = row;\n\t\t\t}\n\t\t\t_results.errors.push(error);\n\t\t}\n\t}\n\n\t/** https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions */\n\tfunction escapeRegExp(string)\n\t{\n\t\treturn string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n\t}\n\n\t/** The core parser implements speedy and correct CSV parsing */\n\tfunction Parser(config)\n\t{\n\t\t// Unpack the config object\n\t\tconfig = config || {};\n\t\tvar delim = config.delimiter;\n\t\tvar newline = config.newline;\n\t\tvar comments = config.comments;\n\t\tvar step = config.step;\n\t\tvar preview = config.preview;\n\t\tvar fastMode = config.fastMode;\n\t\tvar quoteChar;\n\t\tif (config.quoteChar === undefined || config.quoteChar === null) {\n\t\t\tquoteChar = '\"';\n\t\t} else {\n\t\t\tquoteChar = config.quoteChar;\n\t\t}\n\t\tvar escapeChar = quoteChar;\n\t\tif (config.escapeChar !== undefined) {\n\t\t\tescapeChar = config.escapeChar;\n\t\t}\n\n\t\t// Delimiter must be valid\n\t\tif (typeof delim !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(delim) > -1)\n\t\t\tdelim = ',';\n\n\t\t// Comment character must be valid\n\t\tif (comments === delim)\n\t\t\tthrow new Error('Comment character same as delimiter');\n\t\telse if (comments === true)\n\t\t\tcomments = '#';\n\t\telse if (typeof comments !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(comments) > -1)\n\t\t\tcomments = false;\n\n\t\t// Newline must be valid: \\r, \\n, or \\r\\n\n\t\tif (newline !== '\\n' && newline !== '\\r' && newline !== '\\r\\n')\n\t\t\tnewline = '\\n';\n\n\t\t// We're gonna need these at the Parser scope\n\t\tvar cursor = 0;\n\t\tvar aborted = false;\n\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\t// For some reason, in Chrome, this speeds things up (!?)\n\t\t\tif (typeof input !== 'string')\n\t\t\t\tthrow new Error('Input must be a string');\n\n\t\t\t// We don't need to compute some of these every time parse() is called,\n\t\t\t// but having them in a more local scope seems to perform better\n\t\t\tvar inputLen = input.length,\n\t\t\t\tdelimLen = delim.length,\n\t\t\t\tnewlineLen = newline.length,\n\t\t\t\tcommentsLen = comments.length;\n\t\t\tvar stepIsFunction = isFunction(step);\n\n\t\t\t// Establish starting state\n\t\t\tcursor = 0;\n\t\t\tvar data = [], errors = [], row = [], lastCursor = 0;\n\n\t\t\tif (!input)\n\t\t\t\treturn returnable();\n\n\t\t\t// Rename headers if there are duplicates\n\t\t\tif (config.header && !baseIndex)\n\t\t\t{\n\t\t\t\tvar firstLine = input.split(newline)[0];\n\t\t\t\tvar headers = firstLine.split(delim);\n\t\t\t\tvar separator = '_';\n\t\t\t\tvar headerMap = [];\n\t\t\t\tvar headerCount = {};\n\t\t\t\tvar duplicateHeaders = false;\n\n\t\t\t\tfor (var j in headers) {\n\t\t\t\t\tvar header = headers[j];\n\t\t\t\t\tif (isFunction(config.transformHeader))\n\t\t\t\t\t\theader = config.transformHeader(header, j);\n\t\t\t\t\tvar headerName = header;\n\n\t\t\t\t\tvar count = headerCount[header] || 0;\n\t\t\t\t\tif (count > 0) {\n\t\t\t\t\t\tduplicateHeaders = true;\n\t\t\t\t\t\theaderName = header + separator + count;\n\t\t\t\t\t}\n\t\t\t\t\theaderCount[header] = count + 1;\n\t\t\t\t\t// In case it already exists, we add more separtors\n\t\t\t\t\twhile (headerMap.includes(headerName)) {\n\t\t\t\t\t\theaderName = headerName + separator + count;\n\t\t\t\t\t}\n\t\t\t\t\theaderMap.push(headerName);\n\t\t\t\t}\n\t\t\t\tif (duplicateHeaders) {\n\t\t\t\t\tvar editedInput = input.split(newline);\n\t\t\t\t\teditedInput[0] = headerMap.join(delim);\n\t\t\t\t\tinput = editedInput.join(newline);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (fastMode || (fastMode !== false && input.indexOf(quoteChar) === -1))\n\t\t\t{\n\t\t\t\tvar rows = input.split(newline);\n\t\t\t\tfor (var i = 0; i < rows.length; i++)\n\t\t\t\t{\n\t\t\t\t\trow = rows[i];\n\t\t\t\t\tcursor += row.length;\n\t\t\t\t\tif (i !== rows.length - 1)\n\t\t\t\t\t\tcursor += newline.length;\n\t\t\t\t\telse if (ignoreLastRow)\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tif (comments && row.substring(0, commentsLen) === comments)\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = [];\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\tif (preview && i >= preview)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = data.slice(0, preview);\n\t\t\t\t\t\treturn returnable(true);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\tvar nextDelim = input.indexOf(delim, cursor);\n\t\t\tvar nextNewline = input.indexOf(newline, cursor);\n\t\t\tvar quoteCharRegex = new RegExp(escapeRegExp(escapeChar) + escapeRegExp(quoteChar), 'g');\n\t\t\tvar quoteSearch = input.indexOf(quoteChar, cursor);\n\n\t\t\t// Parser loop\n\t\t\tfor (;;)\n\t\t\t{\n\t\t\t\t// Field has opening quote\n\t\t\t\tif (input[cursor] === quoteChar)\n\t\t\t\t{\n\t\t\t\t\t// Start our search for the closing quote where the cursor is\n\t\t\t\t\tquoteSearch = cursor;\n\n\t\t\t\t\t// Skip the opening quote\n\t\t\t\t\tcursor++;\n\n\t\t\t\t\tfor (;;)\n\t\t\t\t\t{\n\t\t\t\t\t\t// Find closing quote\n\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, quoteSearch + 1);\n\n\t\t\t\t\t\t//No other quotes are found - no other delimiters\n\t\t\t\t\t\tif (quoteSearch === -1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tif (!ignoreLastRow) {\n\t\t\t\t\t\t\t\t// No closing quote... what a pity\n\t\t\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\t\t\tcode: 'MissingQuotes',\n\t\t\t\t\t\t\t\t\tmessage: 'Quoted field unterminated',\n\t\t\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn finish();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Closing quote at EOF\n\t\t\t\t\t\tif (quoteSearch === inputLen - 1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvar value = input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar);\n\t\t\t\t\t\t\treturn finish(value);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If this quote is escaped, it's part of the data; skip it\n\t\t\t\t\t\t// If the quote character is the escape character, then check if the next character is the escape character\n\t\t\t\t\t\tif (quoteChar === escapeChar &&  input[quoteSearch + 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If the quote character is not the escape character, then check if the previous character was the escape character\n\t\t\t\t\t\tif (quoteChar !== escapeChar && quoteSearch !== 0 && input[quoteSearch - 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif(nextDelim !== -1 && nextDelim < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(nextNewline !== -1 && nextNewline < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// Check up to nextDelim or nextNewline, whichever is closest\n\t\t\t\t\t\tvar checkUpTo = nextNewline === -1 ? nextDelim : Math.min(nextDelim, nextNewline);\n\t\t\t\t\t\tvar spacesBetweenQuoteAndDelimiter = extraSpaces(checkUpTo);\n\n\t\t\t\t\t\t// Closing quote followed by delimiter or 'unnecessary spaces + delimiter'\n\t\t\t\t\t\tif (input.substr(quoteSearch + 1 + spacesBetweenQuoteAndDelimiter, delimLen) === delim)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tcursor = quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen;\n\n\t\t\t\t\t\t\t// If char after following delimiter is not quoteChar, we find next quote char position\n\t\t\t\t\t\t\tif (input[quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen] !== quoteChar)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar spacesBetweenQuoteAndNewLine = extraSpaces(nextNewline);\n\n\t\t\t\t\t\t// Closing quote followed by newline or 'unnecessary spaces + newLine'\n\t\t\t\t\t\tif (input.substring(quoteSearch + 1 + spacesBetweenQuoteAndNewLine, quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen) === newline)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tsaveRow(quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen);\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\t// because we may have skipped the nextDelim in the quoted field\n\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\t// we search for first quote in next line\n\n\t\t\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t\t// Checks for valid closing quotes are complete (escaped quotes or quote followed by EOF/delimiter/newline) -- assume these quotes are part of an invalid text string\n\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\tcode: 'InvalidQuotes',\n\t\t\t\t\t\t\tmessage: 'Trailing quote on quoted field is malformed',\n\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Comment found at start of new line\n\t\t\t\tif (comments && row.length === 0 && input.substring(cursor, cursor + commentsLen) === comments)\n\t\t\t\t{\n\t\t\t\t\tif (nextNewline === -1)\t// Comment ends at EOF\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tcursor = nextNewline + newlineLen;\n\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Next delimiter comes before next newline, so we've reached end of field\n\t\t\t\tif (nextDelim !== -1 && (nextDelim < nextNewline || nextNewline === -1))\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextDelim));\n\t\t\t\t\tcursor = nextDelim + delimLen;\n\t\t\t\t\t// we look for next delimiter char\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// End of row\n\t\t\t\tif (nextNewline !== -1)\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextNewline));\n\t\t\t\t\tsaveRow(nextNewline + newlineLen);\n\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\n\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\t}\n\n\n\t\t\treturn finish();\n\n\n\t\t\tfunction pushRow(row)\n\t\t\t{\n\t\t\t\tdata.push(row);\n\t\t\t\tlastCursor = cursor;\n\t\t\t}\n\n\t\t\t/**\n             * checks if there are extra spaces after closing quote and given index without any text\n             * if Yes, returns the number of spaces\n             */\n\t\t\tfunction extraSpaces(index) {\n\t\t\t\tvar spaceLength = 0;\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tvar textBetweenClosingQuoteAndIndex = input.substring(quoteSearch + 1, index);\n\t\t\t\t\tif (textBetweenClosingQuoteAndIndex && textBetweenClosingQuoteAndIndex.trim() === '') {\n\t\t\t\t\t\tspaceLength = textBetweenClosingQuoteAndIndex.length;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn spaceLength;\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the remaining input from cursor to the end into\n\t\t\t * row, saves the row, calls step, and returns the results.\n\t\t\t */\n\t\t\tfunction finish(value)\n\t\t\t{\n\t\t\t\tif (ignoreLastRow)\n\t\t\t\t\treturn returnable();\n\t\t\t\tif (typeof value === 'undefined')\n\t\t\t\t\tvalue = input.substring(cursor);\n\t\t\t\trow.push(value);\n\t\t\t\tcursor = inputLen;\t// important in case parsing is paused\n\t\t\t\tpushRow(row);\n\t\t\t\tif (stepIsFunction)\n\t\t\t\t\tdoStep();\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the current row to the results. It sets the cursor\n\t\t\t * to newCursor and finds the nextNewline. The caller should\n\t\t\t * take care to execute user's step function and check for\n\t\t\t * preview and end parsing if necessary.\n\t\t\t */\n\t\t\tfunction saveRow(newCursor)\n\t\t\t{\n\t\t\t\tcursor = newCursor;\n\t\t\t\tpushRow(row);\n\t\t\t\trow = [];\n\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t}\n\n\t\t\t/** Returns an object with the results, errors, and meta. */\n\t\t\tfunction returnable(stopped)\n\t\t\t{\n\t\t\t\treturn {\n\t\t\t\t\tdata: data,\n\t\t\t\t\terrors: errors,\n\t\t\t\t\tmeta: {\n\t\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\t\tlinebreak: newline,\n\t\t\t\t\t\taborted: aborted,\n\t\t\t\t\t\ttruncated: !!stopped,\n\t\t\t\t\t\tcursor: lastCursor + (baseIndex || 0)\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t/** Executes the user's step function and resets data & errors. */\n\t\t\tfunction doStep()\n\t\t\t{\n\t\t\t\tstep(returnable());\n\t\t\t\tdata = [];\n\t\t\t\terrors = [];\n\t\t\t}\n\t\t};\n\n\t\t/** Sets the abort flag */\n\t\tthis.abort = function()\n\t\t{\n\t\t\taborted = true;\n\t\t};\n\n\t\t/** Gets the cursor position */\n\t\tthis.getCharIndex = function()\n\t\t{\n\t\t\treturn cursor;\n\t\t};\n\t}\n\n\n\tfunction newWorker()\n\t{\n\t\tif (!Papa.WORKERS_SUPPORTED)\n\t\t\treturn false;\n\n\t\tvar workerUrl = getWorkerBlob();\n\t\tvar w = new global.Worker(workerUrl);\n\t\tw.onmessage = mainThreadReceivedMessage;\n\t\tw.id = workerIdCounter++;\n\t\tworkers[w.id] = w;\n\t\treturn w;\n\t}\n\n\t/** Callback when main thread receives a message */\n\tfunction mainThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\t\tvar worker = workers[msg.workerId];\n\t\tvar aborted = false;\n\n\t\tif (msg.error)\n\t\t\tworker.userError(msg.error, msg.file);\n\t\telse if (msg.results && msg.results.data)\n\t\t{\n\t\t\tvar abort = function() {\n\t\t\t\taborted = true;\n\t\t\t\tcompleteWorker(msg.workerId, { data: [], errors: [], meta: { aborted: true } });\n\t\t\t};\n\n\t\t\tvar handle = {\n\t\t\t\tabort: abort,\n\t\t\t\tpause: notImplemented,\n\t\t\t\tresume: notImplemented\n\t\t\t};\n\n\t\t\tif (isFunction(worker.userStep))\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < msg.results.data.length; i++)\n\t\t\t\t{\n\t\t\t\t\tworker.userStep({\n\t\t\t\t\t\tdata: msg.results.data[i],\n\t\t\t\t\t\terrors: msg.results.errors,\n\t\t\t\t\t\tmeta: msg.results.meta\n\t\t\t\t\t}, handle);\n\t\t\t\t\tif (aborted)\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tdelete msg.results;\t// free memory ASAP\n\t\t\t}\n\t\t\telse if (isFunction(worker.userChunk))\n\t\t\t{\n\t\t\t\tworker.userChunk(msg.results, handle, msg.file);\n\t\t\t\tdelete msg.results;\n\t\t\t}\n\t\t}\n\n\t\tif (msg.finished && !aborted)\n\t\t\tcompleteWorker(msg.workerId, msg.results);\n\t}\n\n\tfunction completeWorker(workerId, results) {\n\t\tvar worker = workers[workerId];\n\t\tif (isFunction(worker.userComplete))\n\t\t\tworker.userComplete(results);\n\t\tworker.terminate();\n\t\tdelete workers[workerId];\n\t}\n\n\tfunction notImplemented() {\n\t\tthrow new Error('Not implemented.');\n\t}\n\n\t/** Callback when worker thread receives a message */\n\tfunction workerThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\n\t\tif (typeof Papa.WORKER_ID === 'undefined' && msg)\n\t\t\tPapa.WORKER_ID = msg.workerId;\n\n\t\tif (typeof msg.input === 'string')\n\t\t{\n\t\t\tglobal.postMessage({\n\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\tresults: Papa.parse(msg.input, msg.config),\n\t\t\t\tfinished: true\n\t\t\t});\n\t\t}\n\t\telse if ((global.File && msg.input instanceof File) || msg.input instanceof Object)\t// thank you, Safari (see issue #106)\n\t\t{\n\t\t\tvar results = Papa.parse(msg.input, msg.config);\n\t\t\tif (results)\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tresults: results,\n\t\t\t\t\tfinished: true\n\t\t\t\t});\n\t\t}\n\t}\n\n\t/** Makes a deep copy of an array or object (mostly) */\n\tfunction copy(obj)\n\t{\n\t\tif (typeof obj !== 'object' || obj === null)\n\t\t\treturn obj;\n\t\tvar cpy = Array.isArray(obj) ? [] : {};\n\t\tfor (var key in obj)\n\t\t\tcpy[key] = copy(obj[key]);\n\t\treturn cpy;\n\t}\n\n\tfunction bindFunction(f, self)\n\t{\n\t\treturn function() { f.apply(self, arguments); };\n\t}\n\n\tfunction isFunction(func)\n\t{\n\t\treturn typeof func === 'function';\n\t}\n\n\treturn Papa;\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/papaparse/papaparse.js\n");

/***/ })

};
;