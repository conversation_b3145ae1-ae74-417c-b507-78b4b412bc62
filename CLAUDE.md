# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server on http://localhost:3000
- `npm run build` - Build the application for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint to check code quality

## Project Architecture

This is a Next.js 14 React application that processes social media data exports and converts them to CSV format. The app supports Twitter, YouTube, Instagram, Facebook, and LinkedIn data.

### Key Components

- **SocialMediaProcessor** (`src/components/SocialMediaProcessor.tsx`) - Main component that handles file upload, platform selection, and data processing for all social media platforms
- **TwitterDownloader** (`src/components/TwitterDownloader.tsx`) - Legacy component specifically for Twitter data processing (superseded by SocialMediaProcessor)

### Data Processing Architecture

The application uses platform-specific configuration objects in `PLATFORM_CONFIGS` that define:
- Field mappings for CSV output
- Pattern recognition logic for each platform's text format
- Data extraction methods specific to each platform's export structure

### Technology Stack

- **Framework**: Next.js 14 with App Router
- **UI**: React with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **CSV Processing**: PapaParse library
- **External API**: OpenAI API (requires user-provided API key)

### File Structure

- `/src/app/` - Next.js app router pages and layout
- `/src/components/` - React components including main processor and UI components
- `/src/components/ui/` - shadcn/ui component library
- `/src/lib/utils.ts` - Utility functions (mainly Tailwind class merging)
- `/src/data/` - Contains sample data and patterns for different platforms

### Data Flow

1. User selects social media platform
2. OpenAI API key validation occurs
3. User uploads text file containing exported social media data
4. Platform-specific parser processes the text using pattern recognition
5. Structured data is converted to CSV format using PapaParse
6. CSV file is automatically downloaded

### API Key Management

The application requires an OpenAI API key that is:
- Stored in localStorage
- Validated against OpenAI's API before use
- Required for all processing operations